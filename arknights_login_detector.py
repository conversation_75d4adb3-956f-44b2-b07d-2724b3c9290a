#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
明日方舟登录状态检测器
用于检测明日方舟游戏是否在线，避免多人共用账号时的冲突
"""

import psutil
import json
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import os
import socket
import subprocess
import sys
import logging
from pathlib import Path

class ArknightsLoginDetector:
    def __init__(self):
        self.status_file = "arknights_status.json"
        self.config_file = "config.json"
        self.log_file = "arknights_detector.log"
        self.is_running = False
        self.current_user = os.getenv('USERNAME', 'Unknown')
        self.game_processes = [
            "ArknightsEndfield.exe",  # 明日方舟：终末地
            "Arknights.exe",          # 明日方舟
            "ArknightsClient.exe",    # 可能的客户端名称
            "hypergryph.exe"          # 鹰角网络相关进程
        ]
        # 模拟器进程列表
        self.emulator_processes = [
            "MuMuPlayer.exe",         # MuMu模拟器
            "MuMuVMMHeadless.exe",    # MuMu虚拟机
            "NoxVMHandle.exe",        # 夜神模拟器
            "BlueStacks.exe",         # 蓝叠模拟器
            "LdPlayer.exe",           # 雷电模拟器
            "MEmu.exe",               # 逍遥模拟器
            "AndroidEmulator.exe"     # 通用安卓模拟器
        ]
        self.setup_logging()
        self.load_config()

    def setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("明日方舟登录检测器启动")

    def load_config(self):
        """加载配置文件"""
        default_config = {
            "check_interval": 5,  # 检查间隔（秒）
            "user_name": self.current_user,
            "auto_start": True,
            "show_notifications": True
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = default_config
                self.save_config()
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            self.config = default_config
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"配置文件保存失败: {e}")
    
    def check_game_process(self):
        """检查明日方舟游戏进程是否运行"""
        try:
            # 首先检查PC端游戏进程
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and any(game_name.lower() in proc.info['name'].lower()
                                           for game_name in self.game_processes):
                    return True, f"PC端: {proc.info['name']}"

            # 检查模拟器进程
            emulator_running = False
            emulator_name = None
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and any(emu_name.lower() in proc.info['name'].lower()
                                           for emu_name in self.emulator_processes):
                    emulator_running = True
                    emulator_name = proc.info['name']
                    break

            if emulator_running:
                # 如果模拟器在运行，检查是否有明日方舟相关的网络活动
                # 这里我们假设如果模拟器在运行且有网络连接，可能在玩明日方舟
                if self.check_emulator_arknights_activity():
                    return True, f"模拟器: {emulator_name}"
                else:
                    # 模拟器运行但可能没有明日方舟，返回模拟器状态供用户判断
                    return "emulator_only", f"模拟器运行: {emulator_name}"

            return False, None
        except Exception as e:
            self.logger.error(f"进程检查失败: {e}")
            return False, None

    def check_emulator_arknights_activity(self):
        """检查模拟器中是否有明日方舟活动"""
        try:
            # 检查是否有明日方舟相关的网络连接
            # 这是一个简化的检测方法，实际可以更复杂
            connections = psutil.net_connections()
            arknights_ports = [443, 80, 8080]  # 明日方舟可能使用的端口

            for conn in connections:
                if conn.status == 'ESTABLISHED' and conn.raddr:
                    # 检查是否连接到明日方舟服务器相关的端口
                    if conn.raddr.port in arknights_ports:
                        return True

            # 如果检测不到网络活动，默认认为模拟器中可能在运行明日方舟
            # 这里可以根据需要调整策略
            return True
        except Exception as e:
            self.logger.warning(f"模拟器活动检查失败: {e}")
            return True  # 默认认为有活动
    
    def check_network_connection(self):
        """检查网络连接状态"""
        try:
            # 尝试连接到常见的游戏服务器或网络检测服务
            test_hosts = [
                ("*******", 53),      # Google DNS
                ("***************", 53),  # 114 DNS
                ("ak.hypergryph.com", 80)  # 明日方舟官网
            ]
            
            for host, port in test_hosts:
                try:
                    sock = socket.create_connection((host, port), timeout=3)
                    sock.close()
                    return True
                except:
                    continue
            return False
        except Exception as e:
            print(f"网络检查失败: {e}")
            return False
    
    def get_current_status(self):
        """获取当前登录状态"""
        game_result, process_name = self.check_game_process()
        network_connected = self.check_network_connection()

        # 处理不同的游戏状态
        if game_result == "emulator_only":
            game_running = False
            emulator_running = True
            is_online = False
        elif game_result:
            game_running = True
            emulator_running = "模拟器" in str(process_name) if process_name else False
            is_online = game_running and network_connected
        else:
            game_running = False
            emulator_running = False
            is_online = False

        status = {
            "timestamp": datetime.now().isoformat(),
            "user": self.config.get("user_name", self.current_user),
            "game_running": game_running,
            "emulator_running": emulator_running,
            "process_name": process_name,
            "network_connected": network_connected,
            "is_online": is_online,
            "computer_name": socket.gethostname()
        }

        return status
    
    def save_status(self, status):
        """保存状态到文件"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"状态保存失败: {e}")
    
    def load_status(self):
        """从文件加载状态"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            print(f"状态加载失败: {e}")
            return None
    
    def monitor_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                status = self.get_current_status()
                self.save_status(status)
                
                # 更新GUI
                if hasattr(self, 'update_gui_callback'):
                    self.update_gui_callback(status)
                
                time.sleep(self.config.get("check_interval", 5))
            except Exception as e:
                print(f"监控循环错误: {e}")
                time.sleep(5)
    
    def start_monitoring(self):
        """开始监控"""
        if not self.is_running:
            self.is_running = True
            self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
            self.monitor_thread.start()
            print("开始监控明日方舟登录状态...")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=1)
        print("停止监控")

class ArknightsGUI:
    def __init__(self):
        self.detector = ArknightsLoginDetector()
        self.detector.update_gui_callback = self.update_display
        
        self.root = tk.Tk()
        self.root.title("明日方舟登录状态检测器")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        self.setup_gui()
        self.detector.start_monitoring()
    
    def setup_gui(self):
        """设置GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="明日方舟登录状态检测", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 状态显示区域
        self.status_frame = ttk.LabelFrame(main_frame, text="当前状态", padding="10")
        self.status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态标签
        self.status_label = ttk.Label(self.status_frame, text="检测中...", 
                                     font=("Arial", 12))
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.user_label = ttk.Label(self.status_frame, text="用户: 未知")
        self.user_label.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        self.time_label = ttk.Label(self.status_frame, text="时间: --")
        self.time_label.grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        
        # 详细信息
        self.detail_frame = ttk.LabelFrame(main_frame, text="详细信息", padding="10")
        self.detail_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.game_status_label = ttk.Label(self.detail_frame, text="游戏进程: 未检测")
        self.game_status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.network_status_label = ttk.Label(self.detail_frame, text="网络连接: 未检测")
        self.network_status_label.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        self.refresh_btn = ttk.Button(button_frame, text="手动刷新", 
                                     command=self.manual_refresh)
        self.refresh_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.config_btn = ttk.Button(button_frame, text="设置", 
                                    command=self.open_config)
        self.config_btn.grid(row=0, column=1)
    
    def update_display(self, status):
        """更新显示内容"""
        try:
            # 主状态
            if status['is_online']:
                status_text = "🟢 已登录在线"
                status_color = "green"
            elif status['game_running']:
                status_text = "🟡 游戏运行中（离线）"
                status_color = "orange"
            elif status.get('emulator_running', False):
                status_text = "🟠 模拟器运行中"
                status_color = "darkorange"
            else:
                status_text = "🔴 未登录"
                status_color = "red"

            self.status_label.config(text=status_text, foreground=status_color)
            self.user_label.config(text=f"用户: {status['user']}")

            # 时间
            timestamp = datetime.fromisoformat(status['timestamp'])
            time_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.config(text=f"更新时间: {time_str}")

            # 详细信息
            if status['game_running']:
                game_text = "游戏状态: 运行中"
            elif status.get('emulator_running', False):
                game_text = "游戏状态: 模拟器运行中"
            else:
                game_text = "游戏状态: 未运行"

            if status['process_name']:
                game_text += f" ({status['process_name']})"
            self.game_status_label.config(text=game_text)

            network_text = "网络连接: " + ("正常" if status['network_connected'] else "异常")
            self.network_status_label.config(text=network_text)

        except Exception as e:
            print(f"GUI更新失败: {e}")
    
    def manual_refresh(self):
        """手动刷新状态"""
        status = self.detector.get_current_status()
        self.detector.save_status(status)
        self.update_display(status)
    
    def open_config(self):
        """打开配置窗口"""
        config_window = tk.Toplevel(self.root)
        config_window.title("设置")
        config_window.geometry("300x200")
        config_window.resizable(False, False)
        
        # 用户名设置
        ttk.Label(config_window, text="用户名:").grid(row=0, column=0, padx=10, pady=10, sticky=tk.W)
        user_var = tk.StringVar(value=self.detector.config.get("user_name", ""))
        user_entry = ttk.Entry(config_window, textvariable=user_var, width=20)
        user_entry.grid(row=0, column=1, padx=10, pady=10)
        
        # 检查间隔设置
        ttk.Label(config_window, text="检查间隔(秒):").grid(row=1, column=0, padx=10, pady=10, sticky=tk.W)
        interval_var = tk.StringVar(value=str(self.detector.config.get("check_interval", 5)))
        interval_entry = ttk.Entry(config_window, textvariable=interval_var, width=20)
        interval_entry.grid(row=1, column=1, padx=10, pady=10)
        
        def save_config():
            try:
                self.detector.config["user_name"] = user_var.get()
                self.detector.config["check_interval"] = int(interval_var.get())
                self.detector.save_config()
                messagebox.showinfo("成功", "配置已保存")
                config_window.destroy()
            except ValueError:
                messagebox.showerror("错误", "检查间隔必须是数字")
        
        ttk.Button(config_window, text="保存", command=save_config).grid(row=2, column=0, columnspan=2, pady=20)
    
    def run(self):
        """运行GUI"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
    
    def on_closing(self):
        """关闭程序"""
        self.detector.stop_monitoring()
        self.root.destroy()

if __name__ == "__main__":
    try:
        app = ArknightsGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")
