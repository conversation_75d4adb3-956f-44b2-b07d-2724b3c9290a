@echo off
chcp 65001 >nul
title 明日方舟登录状态检测器 - 安装和运行
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    明日方舟登录状态检测器                    ║
echo ║                        安装和运行脚本                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否安装
echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python
    echo.
    echo 请先安装Python 3.7或更高版本:
    echo 下载地址: https://www.python.org/downloads/
    echo.
    echo 安装时请勾选 "Add Python to PATH" 选项
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo ✅ Python版本: %%i
)

REM 检查pip
echo [2/4] 检查pip包管理器...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip不可用，尝试修复...
    python -m ensurepip --upgrade
) else (
    echo ✅ pip可用
)

REM 安装依赖
echo [3/4] 安装依赖包...
if exist requirements.txt (
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    ) else (
        echo ✅ 依赖安装成功
    )
) else (
    echo ⚠️ 未找到requirements.txt，手动安装psutil...
    pip install psutil
)

REM 运行测试
echo [4/4] 运行功能测试...
if exist test_detector.py (
    python test_detector.py
    if %errorlevel% neq 0 (
        echo ❌ 功能测试失败
        pause
        exit /b 1
    )
) else (
    echo ⚠️ 未找到测试文件，跳过测试
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        安装完成！                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 询问是否启动程序
set /p choice="是否现在启动程序？(Y/N): "
if /i "%choice%"=="Y" (
    echo.
    echo 🚀 启动明日方舟登录检测器...
    echo.
    python arknights_login_detector.py
) else (
    echo.
    echo 💡 稍后可以运行以下命令启动程序:
    echo    python arknights_login_detector.py
    echo.
    echo 或者双击 start.bat 文件
)

echo.
pause
