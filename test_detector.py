#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
明日方舟登录检测器测试脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from arknights_login_detector import ArknightsLoginDetector
    print("✅ 成功导入ArknightsLoginDetector")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 开始测试基本功能...")
    
    try:
        # 创建检测器实例
        detector = ArknightsLoginDetector()
        print("✅ 检测器实例创建成功")
        
        # 测试进程检查
        game_running, process_name = detector.check_game_process()
        print(f"🎮 游戏进程检查: {'运行中' if game_running else '未运行'}")
        if process_name:
            print(f"   进程名称: {process_name}")
        
        # 测试网络连接
        network_ok = detector.check_network_connection()
        print(f"🌐 网络连接检查: {'正常' if network_ok else '异常'}")
        
        # 测试状态获取
        status = detector.get_current_status()
        print(f"📊 状态获取成功:")
        print(f"   用户: {status['user']}")
        print(f"   游戏运行: {status['game_running']}")
        print(f"   网络连接: {status['network_connected']}")
        print(f"   在线状态: {status['is_online']}")
        print(f"   计算机名: {status['computer_name']}")
        
        # 测试状态保存和加载
        detector.save_status(status)
        loaded_status = detector.load_status()
        if loaded_status:
            print("✅ 状态文件保存和加载成功")
        else:
            print("❌ 状态文件保存或加载失败")
        
        # 测试配置
        detector.save_config()
        print("✅ 配置文件保存成功")
        
        print("\n🎉 所有基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gui_import():
    """测试GUI模块导入"""
    print("\n🖥️ 测试GUI模块...")
    
    try:
        import tkinter as tk
        print("✅ tkinter模块可用")
        
        # 测试是否可以创建窗口（不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("✅ GUI窗口创建测试成功")
        
        return True
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        print("   注意: 如果在无图形界面的环境中运行，这是正常的")
        return False

def main():
    """主测试函数"""
    print("🚀 明日方舟登录检测器 - 功能测试")
    print("=" * 50)
    
    # 测试基本功能
    basic_ok = test_basic_functionality()
    
    # 测试GUI
    gui_ok = test_gui_import()
    
    print("\n📋 测试结果总结:")
    print(f"   基本功能: {'✅ 通过' if basic_ok else '❌ 失败'}")
    print(f"   GUI功能: {'✅ 通过' if gui_ok else '❌ 失败'}")
    
    if basic_ok:
        print("\n🎯 核心功能正常，程序可以使用！")
        print("💡 运行 'python arknights_login_detector.py' 启动完整程序")
    else:
        print("\n⚠️ 存在问题，请检查错误信息")
    
    print("\n📁 生成的文件:")
    files = ['arknights_status.json', 'config.json', 'arknights_detector.log']
    for file in files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} (未生成)")

if __name__ == "__main__":
    main()
