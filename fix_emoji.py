#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复emoji显示问题的脚本
"""

# 修复主程序文件
with open('arknights_login_detector.py', 'r', encoding='utf-8') as f:
    content = f.read()

content = content.replace('status_text = "� 模拟器运行中"', 'status_text = "🟠 模拟器运行中"')

with open('arknights_login_detector.py', 'w', encoding='utf-8') as f:
    f.write(content)

# 修复README文件
with open('README.md', 'r', encoding='utf-8') as f:
    readme_content = f.read()

readme_content = readme_content.replace('- � **模拟器运行中**', '- 🟠 **模拟器运行中**')
readme_content = readme_content.replace('- �🔴 **未登录**', '- 🔴 **未登录**')

with open('README.md', 'w', encoding='utf-8') as f:
    f.write(readme_content)

print("✅ 所有文件的Emoji显示问题已修复")
