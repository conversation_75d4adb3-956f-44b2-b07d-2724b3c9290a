# 明日方舟登录状态检测器

这是一个用于检测明日方舟游戏登录状态的工具，专门解决多人共用账号时的冲突问题。

## 功能特性

- 🎮 **游戏进程监控**: 实时检测明日方舟游戏是否运行
- 🌐 **网络状态检测**: 判断游戏是否真正在线
- 👥 **多用户支持**: 显示当前登录用户信息
- 📊 **实时状态显示**: 直观的 GUI 界面显示登录状态
- 💾 **状态共享**: 通过本地文件共享状态，多台电脑可查看
- ⚙️ **可配置**: 支持自定义用户名和检查间隔
- 📝 **日志记录**: 详细的操作日志记录

## 系统要求

- Windows 10/11
- Python 3.7 或更高版本
- 网络连接

## 安装和使用

### 方法一：使用启动脚本（推荐）

1. 下载所有文件到同一个文件夹
2. 双击运行 `start.bat`
3. 脚本会自动检查 Python 环境并安装依赖
4. 程序启动后会显示 GUI 界面

### 方法二：手动安装

1. 确保已安装 Python 3.7+
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```
3. 运行程序：
   ```bash
   python arknights_login_detector.py
   ```

## 使用说明

### 主界面

程序启动后会显示一个简洁的 GUI 界面，包含：

- **当前状态**: 显示登录状态（已登录在线/游戏运行中/未登录）
- **用户信息**: 显示当前用户名和更新时间
- **详细信息**: 显示游戏进程和网络连接状态
- **操作按钮**: 手动刷新和设置按钮

### 状态说明

- 🟢 **已登录在线**: 游戏正在运行且网络连接正常
- 🟡 **游戏运行中（离线）**: 游戏在运行但网络连接异常
- 🟠 **模拟器运行中**: 检测到模拟器运行，可能在玩明日方舟
- 🔴 **未登录**: 游戏和模拟器都未运行

### 设置选项

点击"设置"按钮可以配置：

- **用户名**: 自定义显示的用户名
- **检查间隔**: 设置状态检查的时间间隔（秒）

## 多用户共享

程序会在运行目录生成 `arknights_status.json` 文件，包含当前登录状态信息。

如果多台电脑需要共享状态，可以：

1. 将程序放在共享网络文件夹中
2. 或者定期同步状态文件
3. 每台电脑运行程序时都能看到最新的登录状态

## 文件说明

- `arknights_login_detector.py`: 主程序文件
- `requirements.txt`: Python 依赖包列表
- `start.bat`: Windows 启动脚本
- `config.json`: 配置文件（自动生成）
- `arknights_status.json`: 状态文件（自动生成）
- `arknights_detector.log`: 日志文件（自动生成）

## 支持的游戏

### PC 端游戏进程：

- ArknightsEndfield.exe (明日方舟：终末地)
- Arknights.exe (明日方舟)
- ArknightsClient.exe (可能的客户端名称)
- hypergryph.exe (鹰角网络相关进程)

### 安卓模拟器支持：

- MuMu 模拟器 (MuMuPlayer.exe, MuMuVMMHeadless.exe)
- 夜神模拟器 (NoxVMHandle.exe)
- 蓝叠模拟器 (BlueStacks.exe)
- 雷电模拟器 (LdPlayer.exe)
- 逍遥模拟器 (MEmu.exe)
- 其他安卓模拟器 (AndroidEmulator.exe)

## 故障排除

### 常见问题

1. **程序无法启动**

   - 检查 Python 是否正确安装
   - 确保安装了所需的依赖包

2. **检测不到游戏**

   - 确认游戏进程名称是否在支持列表中
   - 检查游戏是否以管理员权限运行

3. **网络状态显示异常**
   - 检查网络连接
   - 确认防火墙设置

### 日志查看

程序运行时会生成 `arknights_detector.log` 日志文件，包含详细的运行信息，可用于问题诊断。

## 注意事项

- 程序需要读取系统进程信息，某些杀毒软件可能会报警，请添加信任
- 建议将程序放在固定位置运行，避免频繁移动
- 状态文件会实时更新，请勿手动修改

## 更新日志

### v1.0.0

- 初始版本发布
- 支持游戏进程监控
- 支持网络状态检测
- 提供 GUI 界面
- 支持配置和日志功能

## 技术支持

如有问题或建议，请检查日志文件或联系开发者。
