#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI测试脚本 - 创建一个测试窗口来验证布局
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime

def create_test_window():
    """创建测试窗口"""
    root = tk.Tk()
    root.title("明日方舟登录状态检测器 - 测试")
    root.geometry("500x400")
    root.resizable(True, True)
    root.minsize(450, 350)
    
    # 配置根窗口的网格权重
    root.grid_rowconfigure(0, weight=1)
    root.grid_columnconfigure(0, weight=1)
    
    # 主框架
    main_frame = ttk.Frame(root, padding="15")
    main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    # 配置主框架的网格权重
    main_frame.grid_rowconfigure(1, weight=1)
    main_frame.grid_rowconfigure(2, weight=1)
    main_frame.grid_columnconfigure(0, weight=1)
    
    # 标题
    title_label = ttk.Label(main_frame, text="明日方舟登录状态检测", 
                           font=("Microsoft YaHei", 16, "bold"))
    title_label.grid(row=0, column=0, columnspan=2, pady=(0, 15))
    
    # 状态显示区域
    status_frame = ttk.LabelFrame(main_frame, text="当前状态", padding="15")
    status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
    status_frame.grid_columnconfigure(0, weight=1)
    
    # 状态标签
    status_label = ttk.Label(status_frame, text="🟢 已登录在线", 
                            font=("Microsoft YaHei", 12, "bold"), foreground="green")
    status_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 8))
    
    user_label = ttk.Label(status_frame, text="用户: 29556", 
                          font=("Microsoft YaHei", 10))
    user_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
    
    time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    time_label = ttk.Label(status_frame, text=f"更新时间: {time_str}", 
                          font=("Microsoft YaHei", 10))
    time_label.grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
    
    # 详细信息
    detail_frame = ttk.LabelFrame(main_frame, text="详细信息", padding="15")
    detail_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
    detail_frame.grid_columnconfigure(0, weight=1)
    
    game_status_label = ttk.Label(detail_frame, text="游戏状态: 运行中 (模拟器: MuMuVMMHeadless.exe)", 
                                 font=("Microsoft YaHei", 10))
    game_status_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 8))
    
    network_status_label = ttk.Label(detail_frame, text="网络连接: 正常", 
                                    font=("Microsoft YaHei", 10))
    network_status_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
    
    # 按钮区域
    button_frame = ttk.Frame(main_frame)
    button_frame.grid(row=3, column=0, columnspan=2, pady=(15, 0), sticky=(tk.W, tk.E))
    button_frame.grid_columnconfigure(0, weight=1)
    button_frame.grid_columnconfigure(1, weight=1)
    
    refresh_btn = ttk.Button(button_frame, text="手动刷新")
    refresh_btn.grid(row=0, column=0, padx=(0, 10), sticky=(tk.W, tk.E))
    
    config_btn = ttk.Button(button_frame, text="设置")
    config_btn.grid(row=0, column=1, padx=(10, 0), sticky=(tk.W, tk.E))
    
    # 添加关闭按钮
    def close_window():
        root.destroy()
    
    close_btn = ttk.Button(main_frame, text="关闭测试窗口", command=close_window)
    close_btn.grid(row=4, column=0, columnspan=2, pady=(10, 0))
    
    return root

if __name__ == "__main__":
    print("🧪 启动GUI布局测试...")
    test_window = create_test_window()
    print("✅ 测试窗口已创建，请检查布局是否正确")
    test_window.mainloop()
    print("✅ 测试完成")
